#!/bin/bash
set -e

echo "Starting Laravel application setup..."

# Wait for database to be ready (optional - uncomment if using separate DB container)
# echo "Waiting for database..."
# while ! nc -z $DB_HOST $DB_PORT; do sleep 1; done

# Clear all caches
echo "Clearing application caches..."
php artisan config:clear || true
php artisan route:clear || true
php artisan view:clear || true
php artisan cache:clear || true

# Create storage link
echo "Creating storage link..."
php artisan storage:link || true

# Run database migrations
echo "Running database migrations..."
php artisan migrate --force

# Cache configuration for production
echo "Caching configuration..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

echo "Laravel application setup completed!"

# Start the Laravel development server
echo "Starting Laravel development server..."
exec php artisan serve --host=0.0.0.0 --port=8000
